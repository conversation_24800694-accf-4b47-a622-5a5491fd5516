import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom, retry, catchError, timeout } from 'rxjs';
import { AxiosError } from 'axios';
import {
  CreateSessionDto,
  GetHistorySessionsDto,
  GetHistoryMessagesDto,
  FeedbackDto,
} from './dto';

@Injectable()
export class ChatService {
  private readonly logger = new Logger(ChatService.name);

  private readonly baseUrl: string;

  constructor(private readonly httpService: HttpService) {
    // 根据环境变量判断使用的URL
    const region = process.env.REGION || 'local';
    this.baseUrl = this.getBaseUrlByRegion(region);
    this.logger.log(
      `ChatService initialized with baseUrl: ${this.baseUrl} (region: ${region})`
    );

    // 启动时测试连通性
    this.testConnection();
  }

  /**
   * 根据环境获取对应的服务地址
   */
  private getBaseUrlByRegion(region: string): string {
    const urlMap = {
      local: 'http://*************:9607/ai/orchestration/session',
      dev: 'http://*************:9607/ai/orchestration/session',
      sit: 'http://*************:9607/ai/orchestration/session',
      prd: 'http://*************:9607/ai/orchestration/session', // 生产环境地址需要更新
    };

    return urlMap[region] || urlMap.local;
  }

  /**
   * 测试目标服务连通性
   */
  private async testConnection() {
    try {
      this.logger.log('Testing connection to target service...');
      await firstValueFrom(
        this.httpService
          .get(`${this.baseUrl}/health`, {
            timeout: 5000,
          })
          .pipe(
            timeout(5000),
            catchError((error) => {
              this.logger.warn(`Health check failed: ${error.message}`);
              // 不抛出错误，只是记录警告
              return [];
            })
          )
      );
      this.logger.log('Connection test successful');
    } catch (error) {
      this.logger.warn(`Connection test failed: ${error.message}`);
    }
  }

  /**
   * 创建会话
   */
  async createSession(payload: CreateSessionDto, headers: any) {
    const url = `${this.baseUrl}/createSession`;

    this.logger.log(`Creating session with URL: ${url}`);
    this.logger.log(`Payload: ${JSON.stringify(payload)}`);
    this.logger.log(`Headers: ${JSON.stringify(headers)}`);

    try {
      const response = await firstValueFrom(
        this.httpService.post(url, payload, headers)
      );

      this.logger.log(`Response: ${JSON.stringify(response.data)}`);
      return response.data;
    } catch (error) {
      this.logger.error(`CreateSession failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取历史会话列表
   */
  async getHistorySessions(payload: GetHistorySessionsDto, headers: any) {
    try {
      const url = `${this.baseUrl}/getHistorySessions`;
      const response = await firstValueFrom(
        this.httpService.post(url, payload, headers)
      );
      return response.data;
    } catch (error) {
      this.logger.error(`getHistorySessions failed: ${error.message}`);
    }
  }

  /**
   * 获取历史消息详情
   */
  async getHistoryMessages(params: GetHistoryMessagesDto, headers: any) {
    const url = `${this.baseUrl}/getHistoryMessages`;
    const response = await firstValueFrom(
      this.httpService.get(url, { params, headers })
    );
    return response.data;
  }

  /**
   * 点赞/点踩接口
   */
  async feedback(payload: FeedbackDto, headers: any) {
    const url = `${this.baseUrl}/feedback`;
    const response = await firstValueFrom(
      this.httpService.post(url, payload, { headers })
    );
    return response.data;
  }
}
